#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频处理脚本 - 为每个视频生成可用于微调模型的提示词
"""

import os
import csv
import shutil
import re
from pathlib import Path

def generate_prompt_for_nezha_video(part_number):
    """
    为哪吒之魔童闹海的视频片段生成提示词
    根据电影内容和片段编号生成相应的描述
    """
    
    # 根据片段编号生成不同的提示词
    prompts = [
        "ancient Chinese mythology, young Nezha with lotus body, magical powers, celestial palace",
        "dragon king palace underwater, mystical sea creatures, ancient Chinese architecture",
        "Nezha fighting sea monsters, magical fire lotus, dynamic action scene",
        "celestial beings in heaven, floating clouds, golden light, divine atmosphere",
        "ancient Chinese village by the sea, traditional buildings, peaceful scenery",
        "<PERSON><PERSON><PERSON>'s transformation scene, lotus petals swirling, magical energy",
        "underwater battle, sea dragons, turbulent waves, mystical blue lighting",
        "Nezha with fire-tipped spear, heroic pose, dramatic lighting",
        "ancient Chinese court, emperor and officials, traditional costumes",
        "magical lotus pond, blooming flowers, serene water reflection",
        "Nezha flying through clouds, wind and movement, celestial background",
        "sea storm with giant waves, dark clouds, dramatic weather",
        "<PERSON><PERSON><PERSON>'s parents in traditional Chinese clothing, emotional scene",
        "dragon transformation, scales and claws, mystical creature",
        "ancient temple with incense, golden statues, spiritual atmosphere",
        "Nezha as child playing, innocent expression, warm lighting",
        "battle in the sky, flying figures, clouds and lightning",
        "underwater coral palace, colorful sea life, magical glow",
        "Nezha with lotus weapons, combat stance, energy effects",
        "peaceful village life, farmers and fishermen, daily activities",
        "celestial court meeting, gods and immortals, grand hall",
        "Nezha's anger and determination, intense facial expression",
        "sea creatures fleeing, underwater chaos, dark waters",
        "lotus blooming in slow motion, petals falling, beauty",
        "ancient Chinese ships on rough seas, adventure scene",
        "Nezha meditating, spiritual energy, calm atmosphere",
        "dragon king's throne room, majestic and intimidating",
        "village celebration, lanterns and festivities, joyful crowd",
        "Nezha's final battle, epic confrontation, dramatic climax",
        "peaceful resolution, harmony between sea and land, sunset"
    ]
    
    # 循环使用提示词，确保每个视频都有描述
    base_prompt = prompts[part_number % len(prompts)]
    
    # 添加一些变化以避免重复
    variations = [
        f"animated scene of {base_prompt}, Chinese animation style",
        f"cinematic view of {base_prompt}, high quality animation",
        f"detailed animation showing {base_prompt}, vibrant colors",
        f"epic scene featuring {base_prompt}, dramatic composition",
        f"beautiful animation of {base_prompt}, traditional art style"
    ]
    
    return variations[part_number % len(variations)]

def extract_part_number(filename):
    """从文件名中提取Part编号"""
    match = re.search(r'Part(\d+)', filename)
    if match:
        return int(match.group(1))
    return 0

def process_videos():
    """处理所有视频文件"""
    
    # 获取当前目录的父目录（包含所有视频文件的目录）
    parent_dir = Path(__file__).parent.parent
    video_dir = Path(__file__).parent
    
    # 获取所有mp4文件
    video_files = list(parent_dir.glob("*.mp4"))
    video_files.sort(key=lambda x: extract_part_number(x.name))
    
    print(f"找到 {len(video_files)} 个视频文件")
    
    # 准备CSV数据
    csv_data = []
    csv_data.append(["video", "prompt"])  # 添加标题行
    
    # 处理每个视频文件
    for i, video_file in enumerate(video_files):
        old_name = video_file.name
        part_number = extract_part_number(old_name)
        
        # 生成新的文件名
        new_name = f"video{i+1:04d}.mp4"
        new_path = video_dir / new_name
        
        # 复制并重命名视频文件到新文件夹
        try:
            shutil.copy2(video_file, new_path)
            print(f"已复制: {old_name} -> {new_name}")
        except Exception as e:
            print(f"复制文件失败 {old_name}: {e}")
            continue
        
        # 生成提示词
        prompt = generate_prompt_for_nezha_video(part_number)
        
        # 添加到CSV数据
        csv_data.append([new_name, prompt])
        
        # 每处理100个文件显示进度
        if (i + 1) % 100 == 0:
            print(f"已处理 {i + 1} 个文件...")
    
    # 保存CSV文件
    csv_file = video_dir / "video_prompts.csv"
    try:
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerows(csv_data)
        print(f"\nCSV文件已保存: {csv_file}")
        print(f"总共处理了 {len(csv_data)-1} 个视频文件")
    except Exception as e:
        print(f"保存CSV文件失败: {e}")

if __name__ == "__main__":
    print("开始处理视频文件...")
    process_videos()
    print("处理完成!")
